{"name": "@zsmartex/castle", "private": true, "scripts": {"build": "nuxi build", "prepare": "nuxi prepare", "dev": "nuxi dev --port 3100", "start": "NUXT_APP_BASE_URL=/castle/ node .output/server/index.mjs", "lint": "tsc --noEmit && eslint . --ext .js,.jsx,.ts,.tsx,.vue"}, "dependencies": {"@headlessui/vue": "1.7.19", "@heroicons/vue": "2.1.1", "@types/http-proxy": "^1.17.14", "@unocss/nuxt": "^0.63.4", "@zsmartex/components": "workspace:*", "@zsmartex/config": "workspace:*", "@zsmartex/core": "workspace:*", "@zsmartex/i18n": "workspace:*", "@zsmartex/types": "workspace:*", "@zsmartex/utils": "workspace:*", "@zsmartex/draggable": "workspace:*", "axios": "^1.6.2", "cookie": "0.6.0", "currency-symbol-map": "^5.1.0", "date-fns": "2.30.0", "http-proxy": "^1.18.1", "js-cookie": "3.0.5", "mitt": "3.0.1", "pinia": "^2.2.4", "chart.js": "^4.4.0", "vue-chartjs": "^5.3.0", "shvl": "3.0.0", "chartjs-chart-geo": "4.2.7"}, "devDependencies": {"@antfu/eslint-config": "2.2.2", "@iconify-json/carbon": "1.1.24", "@iconify-json/twemoji": "1.1.14", "@pinia/nuxt": "^0.5.5", "@vue/babel-plugin-jsx": "^1.2.5", "@vueuse/nuxt": "^11.1.0", "eslint": "8.55.0", "less": "^4.2.0", "less-loader": "^11.1.3", "nuxt": "^3.13.2", "typescript": "^5.3.2"}, "vite": {"optimizeDeps": {"include": ["mitt", "pinia/dist/pinia.mjs", "vue", "vue-router"]}}}