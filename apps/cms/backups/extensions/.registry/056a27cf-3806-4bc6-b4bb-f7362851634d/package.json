{"name": "@directus-labs/seo-plugin", "type": "module", "version": "1.1.0", "description": "SEO plugin for Directus", "license": "MIT", "keywords": ["directus", "directus-extension", "directus-extension-bundle"], "icon": "extension", "files": ["dist"], "directus:extension": {"type": "bundle", "path": {"app": "dist/app.js", "api": "dist/api.js"}, "entries": [{"type": "interface", "name": "seo-interface", "source": "src/seo-interface/index.ts"}, {"type": "display", "name": "seo-display", "source": "src/seo-display/index.ts"}], "host": "^10.10.0"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify", "link": "directus-extension link", "add": "directus-extension add", "validate": "directus-extension validate"}, "dependencies": {"@directus/format-title": "^12.0.0", "@vueuse/core": "^12.5.0", "lodash-es": "^4.17.21", "micromustache": "^8.0.3", "reka-ui": "^2.2.0", "vue-i18n": "^10.0.5"}, "devDependencies": {"@directus/extensions-sdk": "13.0.1", "@types/lodash-es": "^4.17.12", "sass": "^1.84.0", "typescript": "^5.7.3", "vue": "^3.5.13"}}