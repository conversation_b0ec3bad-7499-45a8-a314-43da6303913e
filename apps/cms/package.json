{"name": "safetrade-cms", "version": "1.0.0", "type": "module", "scripts": {"schema:sync": "tsx schema-manager.ts", "schema:sync:dry": "tsx schema-manager.ts --dry-run", "schema:snapshot": "tsx schema-manager.ts --skip-differences", "schema:update": "tsx schema-updater.ts", "schema:update:dry": "tsx schema-updater.ts --dry-run", "schema:update:force": "tsx schema-updater.ts --force", "build": "tsc", "dev": "tsx watch schema-manager.ts --dry-run"}, "dependencies": {"@directus/sdk": "^19.0.1", "dotenv": "^16.4.7"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0"}}