{"home": {"hero": {"title": "The Global Cryptocurrency Exchange", "description": "Your Crypto Trading Expert", "buttonTrade": "Trade Now", "buttonRegister": "Register Now"}, "ranking": {"title": "Rankings", "hot": "Hot", "new": "New", "gainers": "Gaine<PERSON>", "losers": "Losers", "movers": "Movers", "volume": "Volume", "more": "More", "coins": "Coins", "price": "Price(USD)", "change": "24H Change", "high": "24h High", "low": "24h Low"}, "breaking": {"title": "Breaking News", "more": "More", "announcements": "Announcement", "news": "News", "insight": "Insights", "minutes": "min(s) ago", "hours": "hour(s) ago"}, "figures": {"title": "SafeTrade in Figures", "coins": "Coins", "markets": "Markets", "value": "24-Hour Value(USD)", "value30": "30-Day Value(USD)"}, "download": {"title": "Trade crypto. Anytime. Anywhere. ", "description": "Thousands of assets in the palm of your hand", "description2": "Fast and timely market feeds", "description3": "In-depth market analysis", "button": "Download Now", "qr": "Scan to Download SafeTrade App"}, "services": {"heading": "Crypto trading with users in mind", "title": "Explore more services", "description": "Supporting languages in 200+ countries/regions", "description2": "Beginner-friendly features"}, "getToken": {"title": "Get CET, the token of SafeTrade", "description": "CET is the native token of SafeTrade, the global cryptocurrency exchange.", "latestPrice": "Latest Price(USD)", "totalIssued": "Total Issued(CET)", "circulatingSupply": "Circulating Supply(CET)", "burntRatio": "Burnt Ratio"}, "tradeNow": {"title": "Trade Now", "description": "Trade crypto. Anytime. Anywhere.", "button": "Trade Now", "button2": "Register Now", "buttonTrade": "Trade Now", "buttonRegister": "Register Now"}}, "addressBook": {"title": "Address Book", "addAddress": "Add Address", "addAddressBook": "Add Address Book", "addAddressBookSuccess": "Add Address Book Success", "addAddressBookFailed": "Add Address Book Failed", "currency": "<PERSON><PERSON><PERSON><PERSON>", "address": "Address", "network": "Network", "addressLabel": "Address Label", "actions": "Actions", "withdraw": "Withdraw", "remove": "Remove", "deleteAddressBookSuccess": "Delete Address Book Success", "deleteAddressBookFailed": "Delete Address Book Failed", "addAddressBookDescription": "Add a new address book to your account", "enterEmailCode": "Enter email code", "enterVerificationCode": "Enter verification code", "resendCode": "Resend code", "sendCode": "Send code", "enterCode2FA": "Enter code Two-factor Authentication (2FA)", "verify": "Verify"}, "apiManagement": {"title": "API Management", "apiKey": "API Key", "apiSecret": "API Secret", "apiKeyDescription": "API Key is used to authenticate your requests to the API", "apiSecretDescription": "API Secret is used to authenticate your requests to the API", "label": "Label", "status": "Status", "actions": "Actions", "edit": "Edit", "remove": "Remove", "apiKeyActive": "API Key is active and can be used", "apiKeyDisabled": "API Key is disabled and cannot be used", "createAPIKey": "Create API Key", "editAPIKey": "Edit API Key", "updateAPIKeySettings": "Update your API key settings", "createAPIKeyDescription": "Create a new API key for your account", "enterAPIKeyLabel": "Enter API key label", "permissions": "Permissions", "trustedIPAddresses": "Trusted IP Addresses", "enterIPAddresses": "Enter IP addresses separated by commas (e.g., ***********, ********)", "securityVerificationRequired": "Security Verification Required", "enter2FA": "Please enter your 2FA code to complete the.", "twoFactorAuthentication": "Two-Factor Authentication (2FA)", "updateAPIKey": "Update", "apiKeyUpdated": "API Key Updated Successfully", "apiKeyCreated": "API Key Created Successfully", "apiKeySaved": "Please save your API credentials securely. You won't be able to see the secret again.", "apiKeyID": "API Key ID", "secretKey": "Secret Key", "done": "Done", "failedToCopyToClipboard": "Failed to copy to clipboard", "copiedToClipboard": "{label} copied to clipboard", "fillAllRequiredFields": "Please fill in all required fields correctly", "fillAllFields": "Please fill in all fields", "withdraw": "Withdraw", "withdrawDescription": "Allow withdrawing funds (requires IP restriction)", "addAPIKey": "Add API Key", "next": "Next", "deleteAPIKey": "Delete API Key", "deleteAPIKeyDescription": "This action cannot be undone. The API key will be permanently removed.", "apiKeyDeletedSuccessfully": "API Key Deleted Successfully", "deleteAPIKeyConfirmation": "Are you sure you want to delete this API key?", "deleteAPIKeyConfirmationDescription": "This action cannot be undone. The API key will be permanently removed."}, "fees": {"tradingFees": "Trading Fees", "fees": "Fees", "marketID": "Market ID", "maker": "Maker", "taker": "Taker", "search": "Search", "transactionFees": "Transaction Fees", "coinToken": "Coin/Token", "name": "Name", "networks": "Networks", "minDeposit": "<PERSON>", "depositFee": "Deposit Fee", "minWithdraw": "<PERSON>", "withdrawFee": "Withdraw Fee", "free": "Free"}, "overview": {"accountID": "Account ID", "email": "Email", "accountIDDescription": "Account ID is your unique identifier on the platform", "emailDescription": "Email is your primary contact information on the platform", "emailVerification": "Email Verification", "twoFactorAuthentication": "Two-Factor Authentication (2FA)", "set": "Set", "preferences": "Preferences", "open": "Open", "verify": "Verify", "enabled": "Enabled", "notEnabled": "Not Enabled", "verified": "Verified", "unverified": "Unverified", "loginStatusManagement": "Login Status Management", "device": "<PERSON><PERSON>", "ipAddress": "IP Address", "time": "Time", "state": "State", "action": "Action", "online": "Online", "sessionDeletedSuccessfully": "Session deleted successfully"}, "interactiveMessages": {"title": "Interactive Messages", "description": "Interactive Messages", "button": "Send Message", "markAllAsRead": "Mark all as read", "all": "All", "read": "Read", "unread": "Unread"}, "preferences": {"title": "Preferences", "orderConfirmation": "Order Confirmation", "spotTrading": "Spot Trading", "futuresTrading": "Futures Trading", "useCETAsFees": "Use CET as Fees", "payWithdrawalFee": "Pay Withdrawal Fee", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "darkMode": "Dark Mode", "smsCode": "SMS Code", "emailSubscriptions": "E-mail Subscriptions", "listingAnnouncements": "Listing Announcements", "latestPromotions": "Latest Promotions", "blog": "Blog", "orderConfirmationDescription": "Enable to receive a pop-up Order Confirmation with order details when you place orders.", "set": "Set", "coin": "Coin", "usd": "USD", "english": "English", "vietnamese": "Vietnamese", "chinese": "Chinese", "useCETAsFeesDescription": "By activating 'Use CET as Fees', CET will be applied to pay trading fee per transaction (20%).", "payWithdrawalFeeDescription": "You can customize payment for normal withdrawals", "smsCodeDescription": "When enabled, verification codes will be sent to your mobile phone via SMS only, and won't be sent to other third-party platforms anymore.", "emailSubscriptionsDescription": "Enable to receive email notifications in the following categories.", "listingAnnouncementsDescription": "Enable to receive email notifications in the following categories.", "latestPromotionsDescription": "Enable to receive email notifications in the following categories."}, "security": {"title": "Security Settings", "withdrawalPassword": "<PERSON><PERSON><PERSON> Password", "withdrawalPasswordDescription": "Set withdrawal password to protect your assets", "tradingPassword": "Trading Password", "tradingPasswordDescription": "Set trading password to secure your transactions", "antiPhishingCode": "Anti-Phishing Code", "antiPhishingCodeDescription": "For verifying official emails from SafeTrade", "withdrawalMultiApproval": "Withdrawal Multi-Approval", "withdrawalMultiApprovalDescription": "Adding withdrawal approval Email to co-manage the account", "logInWithThirdPartyAccount": "Log in with a third-party account", "logInWithThirdPartyAccountDescription": "Quick login to SafeTrade with a third-party account", "lockLoginIP": "Lock Login IP", "lockLoginIPDescription": "If activated, the login session on the web will be logged off when switching to a new IP address.", "secureLoginDuration": "Secure Login Duration", "secureLoginDurationDescription": "If activated, the login session on the web will be logged out after one hour of inactivity.", "accountManagement": "Account Management", "accountManagementDescription": "Freeze or delete account", "active": "Active", "manage": "Manage", "notSet": "Not Set", "toggle": "Toggle", "set": "Set", "email": "Email", "password": "Password", "change": "Change", "basicSettings": "Basic Settings", "changeEmail": "Change Email", "changePassword": "Change Password", "passkey": "Passkey", "passkeyDescription": "Use a security device such as Yubikey to protect your account.", "totpVerification": "TOTP Verification", "totpVerificationDescription": "Use TOTP authenticator to protect your account.", "mobile": "Mobile", "mobileDescription": "Use SMS verification to protect your account.", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "twoFactorAuthentication": "Two-factor Authentication (2FA)"}, "snapshots": {"title": "Snapshots", "createSnapshot": "Create Snapshot", "type": "Type", "status": "Status", "timeRange": "Time Range", "createdAt": "Created At", "file": "File", "actions": "Actions", "download": "Download", "downloadSuccess": "Snapshot downloaded successfully", "createSuccess": "Snapshot created successfully", "transactions": "Transactions", "trades": "Trades", "accounts": "Accounts", "allTime": "All time", "createSnapshotDescription": "Create a new snapshot of your account data", "selectSnapshotType": "Select snapshot type", "selectTimeRange": "Select time range", "year": "Year", "selectYear": "Select year", "dateRange": "Date Range", "selectDateRange": "Select date range", "enterCodeTwoFactor": "Enter code Two-factor Authentication (2FA)", "next": "Next", "pleaseSelectSnapshotType": "Please select a snapshot type", "pleaseSelectTimeRange": "Please select a time range", "pleaseSelectYear": "Please select a year", "pleaseSelectDateRange": "Please select a date range", "pleaseFillInAllRequiredFields": "Please fill in all required fields", "enterCodeTwoFactorDescription": "Enter the code from your authenticator app to confirm the action", "past7Days": "Past 7 days", "past30Days": "Past 30 days", "past90Days": "Past 90 days", "past12Months": "Past 12 months", "aCalendarYear": "A calendar year", "customize": "Customize"}, "verify": {"title": "ID Verification", "howToVerify": "How to Verify", "iPromise": "I promise that the information provided is my own, and there is no pirating of other people's information.", "iAmAllSet": "I'm all set for ID Verification", "basicInfo": "Basic Info", "uploadId": "Upload ID", "reminder1": "Please make sure that the info provided is identical to your ID info, and these information cannot be altered once the ID verification is approved.", "reminder2": "If the camera is not installed on the device, please use SafeTrade App for ID Verification.", "reminder3": "Currently, SafeTrade does not support ID Verification for users in China, America, U.S. Minor Outlying Islands, Hong Kong, China, Canada.", "reminder4": "The audit results will be sent to your Email in 1~3 working days. Please check your mailbox.", "withdrawQuota": "24H withdrawal quota: {quota}", "withdrawQuotaDescription": "After passing the ID verification, you will be entitled to the below privileges:"}, "popup": {"twoFactor": {"disable": "Disable 2FA", "disableDescription": "Please enter the email code and the code from your authenticator app to disable 2FA.", "enterEmailCode": "Enter email code", "enterCodeTwoFactor": "Enter code Two-factor Authentication (2FA)", "didntGetCode": "Didn't get code?", "getCode": "Get code", "codeSentSuccessfully": "Code sent successfully", "twoFactorDisabledSuccessfully": "2FA disabled successfully", "emailCodeRequired": "Email code is required", "otpRequired": "OTP is required", "totpSettings": "TOTP Settings", "enterVerificationCode": "Enter verification code", "resendCode": "Resend code", "sendCode": "Send code", "privateKey": "Private key", "step1": "1. Download and install TOTP Authenticator Apps, such as Google Authenticator.", "step2": "2. Open the app, click \"+\" or \"Add account\", and select \"Scan a QR code\".", "note": "Note: The key can be used for TOTP restoring when your Mobile is changed or lost, please safe keep the key. In addition, for your asset security, CoinEx does not provide key recovery service.", "emailVerification": "Email verification", "verificationCode": "Verification code", "successfully": "Successfully", "pleaseEnterCodeSentToYourEmail": "Please enter code sent to your email", "totpSettingsSuccessfully": "TOTP settings successfully", "invalidVerificationEmailCodeOr2faCode": "Invalid verification email code or 2FA code", "secretKeyCopiedToClipboard": "Secret key copied to clipboard", "passkeyAddedSuccessfully": "Passkey added successfully", "next": "Next", "verificationCodePlaceholder": "XXX-XXX", "emailVerificationDescription": "Please enter the email code and the code from your authenticator app to enable 2FA."}, "updateEmail": {"title": "Change email", "description": "Please enter the 6-digit verification code sent to your email", "enterEmailCode": "Enter email code", "enterVerificationCode": "Enter verification code", "enterNewEmail": "Enter new email", "enterNewEmailCode": "Enter new email code", "changeEmail": "Change email", "pleaseVerifyEmailToChangeEmail": "Please verify your email to change email", "verificationCodeSentToYourCurrentEmail": "Verification code sent to your current email", "pleaseEnterYourNewEmail": "Please enter your new email", "verificationCodeSentToYourNewEmail": "Verification code sent to your new email", "emailChangedSuccessfully": "Email changed successfully"}, "updatePassword": {"title": "Change password", "description": "Please enter the 6-digit verification code sent to your email", "enterEmailCode": "Enter email code", "enterVerificationCode": "Enter verification code", "enterNewEmail": "Enter new email", "enterNewEmailCode": "Enter new email code", "changeEmail": "Change email", "pleaseVerifyEmailToChangeEmail": "Please verify your email to change email", "verificationCodeSentToYourCurrentEmail": "Verification code sent to your current email", "pleaseEnterYourNewEmail": "Please enter your new email", "verificationCodeSentToYourNewEmail": "Verification code sent to your new email", "emailChangedSuccessfully": "Email changed successfully"}, "updateUsername": {"title": "Change username", "description": "Please enter your new username", "enterNewUsername": "Enter new username", "enterUsername": "Enter username", "changeUsername": "Change username", "usernameChangedSuccessfully": "Username changed successfully", "usernameChangeFailed": "Username change failed"}}, "auth": {"banner": {"title": "Trade with us today", "description": "More Security 🛡️ Better Privacy. Less Stress.", "feature1": "Buy and trade", "feature2": "Made for mass adoption.", "feature3": "Trade on the go. Anywhere, anytime.", "feature4": "Putting Our Users First"}, "continueWithOtherLogin": "Continue with other login", "termsOfSafeTradeService": "Terms of SafeTrade Service", "byRegisteringAnAccount": "By registering an account, you agree to", "login": {"title": "Log In", "subtitle": "Please log in with your account or scan QR code with SafeTrade App", "signIn": "Sign in"}, "register": {"title": "Create new account", "subtitle": "Start Exploring The Crypto World", "signUp": "Sign up"}, "logout": "Logout", "enterEmail": "Enter email", "pleaseEnterYourAccount": "Please enter your account", "pleaseEnterYourPassword": "Please enter your password", "passwordDoesNotMatch": "Password does not match", "loginSuccessfully": "<PERSON><PERSON> successfully", "confirmCodeSuccessfully": "Confirm code successfully", "pleaseCheckYourEmailToGetTheCode": "Please check your email to get the code", "resetSuccessfully": "Reset successfully", "email": "Email", "newPassword": "New password", "confirmPassword": "Confirm password", "enterCode": "Enter code", "resetPassword": {"title": "Reset password", "subtitle": "The new password has a length of at least 8 characters including 1 letter, 1 special character and 1 number", "button": "Reset password"}, "securityAuthentication": {"title": "Security Authentication", "subtitle": "Please enter the 6-digit OTP code in Authenticator", "button": "Verify"}, "securityEmail": {"title": "Email Verification", "subtitle": "Please enter the 6-digit verification code sent to your email", "button": "Confirm"}, "signIn": "Sign in", "logInWithQRCode": "Log in with QR code", "enterHomeInAppAndScanInTheUpperRightCornerToLogInViaQRCode": "Enter \"Home\" in App, and app \"Scan\" in the upper right corner to log in via QR code", "registerNow": "Register Now", "forgotPassword": "Forgot Password", "alreadyRegistered": "Already registered?", "logInNow": "Log In NOW", "account": "Account", "password": "Password", "enterAccount": "Enter account", "enterPassword": "Enter password", "enterConfirmPassword": "Enter confirm password"}, "contact": {"title": "Contact Us", "subtitle": "Join SafeTrade for more cooperation opportunities", "moreCooperation": "More Cooperation", "followUs": "Follow Us", "copied": "<PERSON>pied", "toClipboard": "to clipboard", "mediaAndCooperation": "Media & Cooperation", "productAndSupport": "Product and Support", "vulnerabilityReport": "Vulnerability Report", "lawEnforcement": "Law Enforcement"}, "exchange": {"announcements": "Announcements", "support": "Support", "orderBook": "Order Book", "24hChange": "24h change", "24hHigh": "24h high", "24hLow": "24h low", "24hAmount": "24h amount", "24hVolume": "24h volume", "amount": "Amount", "sum": "Sum", "buy": "Buy", "sell": "<PERSON>ll", "limit": "Limit", "market": "Market", "stopLimit": "Stop Limit", "currency": "<PERSON><PERSON><PERSON><PERSON>", "price": "Price", "change": "Change", "login": "<PERSON><PERSON>", "register": "Register", "toTrade": "to trade", "marketTrades": "Market Trades", "myTrades": "My Trades", "time": "Time", "priceMustBeGreaterThan": "Price must be greater than", "priceMustBeLessThan": "Price must be less than", "amountMustBeGreaterThan": "Amount must be greater than", "enableSlippage": "Enable Slippage", "setSlippage": "Set Slippage", "confirm": "Confirm", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "total": "Total", "stop": "Stop", "available": "Available", "averagePrice": "Average Price", "tradingView": "TradingView", "depth": "De<PERSON><PERSON>"}, "markets": {"coin": {"recommendedRanks": "Recommended Ranks", "valueLeaders": "Value Leaders", "top24hVolume": "Top 24h Volume", "topGainers": "Top Gainers", "topLosers": "Top Losers", "cryptoList": "Crypto List", "favorites": "Favorites", "topVolume": "Top Volume", "coin": "Coin", "lastPrice": "Last Price(USD)", "24hChange": "24h Change", "24hHigh": "24h High", "24hLow": "24h Low", "24hVolume": "24h Volume", "action": "Action", "updatedFavoriteSuccessfully": "Updated favorite successfully", "trade": "Trade"}, "data": {"backToMarkets": "Back to Markets", "listedCrypto": "Listed Crypto", "coins": "Coins", "24hPriceChange": "24H Price Change", "priceUp": "Price up", "priceDown": "Price down", "unchanged": "Unchanged", "totalMarketCap": "Total Market Cap (USD)", "24hSpotTradingValue": "24H Spot Trading Value (USD)", "topChange": "Top Change", "topVolume": "Top Volume", "historicalMarketValue": "Historical Market Value", "unit": "Unit", "orderDistribution": "Order Distribution", "takerBuy": "Taker Buy", "takerSell": "Taker <PERSON><PERSON>", "priceChangeDistribution": "Price Change Distribution", "topGainers": "Top Gainers", "topLosers": "Top Losers", "valueLeaders": "Value Leaders", "topMarketCap": "Top Market Cap", "topCoins": "Top Coins", "viewAll": "View All", "coin": "Coin", "price": "Price", "24hChange": "24H Change", "cryptoHeatMap": "Crypto Heat Map", "recommendedRanks": "Recommended Ranks"}}, "orders": {"market": "Market", "allMarket": "All Market", "type": "Type", "allType": "All Type", "side": "Side", "allSide": "All Side", "date": "Date", "login": "<PERSON><PERSON>", "or": "or", "register": "Register", "toTrade": "to trade", "state": "State", "avgPrice": "Avg Price", "filled": "Filled", "action": "Action", "amount": "Amount", "total": "Total", "orderCanceledSuccessfully": "Order canceled successfully", "orderCancelFailed": "Order cancel failed", "cancel": "Cancel", "fee": "Fee", "spotOrder": "Spot Order", "open": "Open", "history": "History", "trades": "Trades", "price": "Price", "limit": "Limit", "buy": "Buy", "sell": "<PERSON>ll"}, "support": {"faq": {"frequentlyAskedQuestions": "Frequently Asked Questions", "searchByKeywords": "Search by keywords, e.g. deposit, futures, AMM", "search": "Search"}}, "swap": {"faq": "FAQ", "viewAllFaqs": "View All FAQs", "swap": "<PERSON><PERSON><PERSON>", "swapAssetsEffortlesslyAndSecurely": "Swap assets effortlessly and securely with SafeTrade's self-developed algorithm", "select": "Select", "enterAmount": "Enter amount", "to": "To", "loginToSwap": "Login to Swap", "referencePrice": "Reference Price", "pleaseSelectCurrency": "Please select a currency", "pleaseEnterAmount": "Please enter an amount", "last5Records": "Last 5 Records", "allRecords": "All Records", "date": "Date", "direction": "Direction", "amount": "Amount", "from": "From", "price": "Price", "status": "Status", "operation": "Operation", "trendySwaps": "<PERSON><PERSON><PERSON>"}, "wallet": {"selectAll": "Select All", "spotAssets": "Spot Assets", "totalAssets": "Total Assets", "available": "Available", "locked": "Locked", "selectFromAddressBook": "Select From Address Book", "network": "Network", "selectNetwork": "Select Network", "addWithdrawAddress": "Add Withdraw Address", "safeTradeAccount": "SafeTrade Account", "transferMode": "Transfer Mode", "address": "Address", "assets": "Assets", "time": "Time", "memo": "Memo", "date": "Date", "fee": "Fee", "amount": "Amount", "verifyOtp": "Verify OTP", "verifyOtpDescription": "Please enter the 6-digit verification code sent to your email and 2FA code", "enterEmailCode": "Enter email code", "enterVerificationCode": "Enter verification code", "enterCodeTwoFactorAuthentication": "Enter code Two-factor Authentication (2FA)", "verify": "Verify", "resendCode": "Resend code ({time}s)", "sendCode": "Send code", "notificationCodeSent": "Please check your email for the verification code", "createdAt": "Created At", "sortBy": "Sort by", "allAssets": "All Assets", "export": "Export", "txFee": "Tx. Fee", "txid": "Txid", "depositAddress": "Deposit Address", "credited": "Credited", "status": "Status", "type": "Type", "currency": "<PERSON><PERSON><PERSON><PERSON>", "copiedAddressToClipboard": "Copied address to clipboard", "estTotalAssets": "Est. Total Assets", "todaysPNL": "Today's PNL", "pnlAnalysisActive": "PNL Analysis Active", "activatePNLAnalysis": "Activate PNL Analysis", "buyViaP2P": "Buy via P2P", "buyViaThirdParty": "Buy via Third-party", "assetPortfolio": "Asset Portfolio", "search": "Search", "hideSmallAssets": "<PERSON>de Small Assets", "smallAssetConverter": "Small asset converter", "assetsUnderMaintenance": "Assets under Maintenance", "coin": "Coin", "total": "Total", "btcValue": "BTC Value", "actions": "Actions", "trade": "Trade", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Withdraw", "transfer": "Transfer", "depositHasBeenCreditedToYourAccount": "Deposit has been credited to your account", "amountMustGreaterThan0": "Amount must be greater than 0", "depositFeeNotPaid": "Deposit fee not paid", "youNeed": "You need", "toProcessThisDeposit": "to process this deposit", "youNeedToDepositAdditional": "You need to deposit additional", "depositBelowMinimumAmount": "Deposit below minimum amount", "depositIsBeingProcessed": "Deposit is being processed", "last5DepositRecords": "Last 5 Deposit Records", "allDeposits": "All deposits", "transferNetwork": "Transfer network", "selectTheNetwork": "Select the network", "depositDetails": "Deposit details", "attention": "Attention", "depositFee": "Deposit Fee", "minimumDepositAmount": "Minimum Deposit Amount", "confirmation": "Confirmation(s)", "depositArrival": "Deposit arrival", "noDepositAddressFound": "No deposit address found", "noNetworkFound": "No network found", "minConfirmations": "Min Confirmations", "contractAddress": "Contract Address", "depositNote1": "Please make sure that only {currency} deposit is made via this address. Otherwise, your deposited funds will not be added to your available balance - nor will it be refunded.", "depositNote2": "Please make sure that your SafeTrade {currency} deposit address is correct. Otherwise, your deposited funds will not be added to your available balance - nor will it be refunded.", "note": "Note", "tradeNow": "Trade Now", "retrievingUncreditedDeposit": "Retrieving Uncredited <PERSON><PERSON><PERSON><PERSON>", "depositRecords": "Deposit Records", "normalTransfer": "Normal Transfer", "internalTransfer": "Internal Transfer (0 Fee)", "withdrawToSafeTradeAccount": "Withdraw to a SafeTrade account", "pleaseEnterSafetradeAccount": "Please enter Safetrade account", "withdrawDetails": "Withdraw Details", "withdrawalRequestSentSuccessfully": "<PERSON><PERSON><PERSON> request sent successfully", "pleaseFillInAllRequiredFields": "Please fill in all required fields", "last5WithdrawRecords": "Last 5 Withdraw Records", "allWithdrawals": "All withdrawals", "pleaseSelectNetworkFirst": "Please select a network first", "amountMustBeGreaterThanMinimum": "Amount must be greater than minimum: {minWithdraw} {currency}", "insufficientBalance": "Insufficient balance. Max available: {maxBalance} {currency}", "insufficientBalanceForFee": "Insufficient balance for fee ({fee} {currency}). Please reduce amount.", "pleaseEnterValidAddressAndChooseNetwork": "Please enter a valid address and choose network", "invalidAddressForNetwork": "Invalid {currency} address for {network} network", "couldNotValidateAddress": "Could not validate address. Please check and try again.", "pleaseEnterValidSafeTradeAccount": "Please enter a valid SafeTrade account", "pleaseFillInAllFields": "Please fill in all fields", "pleaseEnterOrSelectWithdrawalAddress": "Please enter or select the withdrawal address", "addressBook": "Address Book", "pleaseEnterYourMemoHere": "Please enter your memo here", "pleaseEnterTheAmount": "Please enter the amount", "pleaseEnterYourWithdrawalNoteHere": "Please enter your withdrawal note here", "withdrawNote1": "Please make sure that only {currency} withdraw is made via this address. Otherwise, your withdrawn funds will not be added to your available balance - nor will it be refunded.", "withdrawNote2": "Please make sure that your SafeTrade {currency} withdraw address is correct. Otherwise, your withdrawn funds will not be added to your available balance - nor will it be refunded.", "withdrawRecords": "Withdraw Records", "withdrawAmount": "Withdraw Amount", "withdrawFee": "Withdraw Fee", "received": "Received", "assetOverview": "Asset Overview", "verified": "Verified", "unverified": "Unverified", "financial": {"tabs": {"financial": "Financial", "staking": "Staking"}, "stats": {"cmlIncomes": "Cml. Incomes (USD)", "lastDayIncome": "Last-day Income (USD)", "avgAPY": "Avg. APY"}, "columns": {"cmlIncomes": "Cml. Incomes", "lastDayIncome": "Last-day Income", "oneDayAPY": "1-Day APY", "sevenDayAPY": "7-Day APY", "operation": "Operation"}}, "amm": {"stats": {"totalIncome": "Total Income (USDT)", "lastDayIncome": "Last-day Income (USDT)", "ammMarkets": "AMM Markets"}, "showMyPairsOnly": "Show My Pairs Only", "columns": {"pair": "Pair", "myLiquidity": "My Liquidity", "totalLiquidity": "Total Liquidity", "position": "Position", "sevenDayFeeGrowth": "7-Day Fee Growth", "oneDayAPY": "1-Day APY", "twoDayAPY": "2-Day APY", "operation": "Operation"}, "actions": {"add": "Add", "remove": "Remove"}}}, "header": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "darkMode": "Dark Mode", "logout": "Logout", "login": "Log In", "signUp": "Sign Up"}, "menu": {"buy-crypto": "Buy Crypto", "p2p-trading": "P2P Trading", "third-party-trading": "Third-party Trading", "markets": "Markets", "market-overview": "Market Overview", "market-data": "Market Data", "feed": "Feed", "insight": "Insight", "exchange": "Exchange", "swap": "<PERSON><PERSON><PERSON>", "spot": "Spot", "pre-token-trading": "Pre-token Trading", "finance": "Finance", "staking": "Staking", "amm": "AMM", "loans": "Loans", "promotion": "Promotion", "trade-board": "Trade Board", "deposit-bonus": "Deposit Bonus", "mining": "Mining", "airdrop-station": "Airdrop Station", "more": "More", "referral": "Referral", "ambassador": "Ambassador", "academy": "Academy", "help-center": "Help Center", "announcements": "Announcements", "assets": "Assets", "orders": "Orders", "account-overview": "Account Overview", "security-setting": "Security Setting", "id-verification": "ID Verification", "preferences": "Preferences", "address-book": "Address Book", "fees": "Fees", "trading-fees": "Trading Fees", "transaction-fees": "Transaction Fees", "api-management": "API Management", "snapshots": "Snapshots", "overview": "Overview", "margin": "<PERSON><PERSON>", "financial": "Financial", "history": "History", "open-order": "Open Order", "history-order": "History Order", "trade-history": "Trade History", "spot-assets": "Spot Assets", "spot-withdraw": "Spot Withdraw", "spot-deposit": "Spot Deposit", "spot-deposit-record": "Spot Deposit Record", "spot-withdraw-record": "Spot Withdraw Record", "spot-transfer-history": "Spot Transfer History", "margin-assets": "<PERSON><PERSON>", "margin-borrowing-records": "Margin Borrowing Records", "financial-assets": "Financial Assets", "financial-records": "Financial Records", "amm-assets": "AMM Assets", "amm-income-records": "AMM Income Records", "amm-records": "AMM Records", "interactiveMessages": "Interactive Messages", "blog": "Blog"}, "notification": {"requireTOTPVerification": "Require TOTP Verification", "requireTOTPVerificationDescription": "For security reasons, please enable TOTP before continuing.", "enableOTP": "Enable OTP"}, "footer": {"company": "Company", "contactUs": "Contact Us", "discord": "Discord", "support": "Support", "faq": "FAQ", "exchange": "Exchange", "wallets": "Wallets", "fees": "Fees", "api": "API", "academy": "Academy", "terms": "Terms", "status": "Status", "helpCenter": "Help Center", "allRightsReserved": "All rights reserved.", "yourCryptoTradingExpert": "Your Crypto Trading Expert"}, "seo": {"home": {"title": "Safe Trade - Buy Crypto, Sell Crypto, Trade Crypto", "description": "Safe Trade is the global cryptocurrency trading platform, offering a secure, fast, and user-friendly experience for every investor."}, "markets-coin": {"title": "Markets Coins", "description": "Explore the dynamic coin markets on Safe Trade – your source for real-time prices, trends, and insights on top cryptocurrencies."}, "markets-data": {"title": "Markets Data Coin", "description": "Access transparent, intuitive crypto market data on Safe Trade to make timely and informed investment decisions."}, "swap": {"title": "<PERSON><PERSON><PERSON>", "description": "Swap digital assets easily and securely with Safe Trade – enjoy fast transactions, low fees, and an optimized user experience."}, "auth-login": {"title": "<PERSON><PERSON> Account", "description": "Log in to Safe Trade to manage your assets and trade crypto securely with advanced protection and a user-friendly interface."}, "exchange": {"title": "Safe Trade Exchange", "description": "Safe Trade Exchange is a secure and efficient platform for trading crypto. Enjoy fast transactions, low fees, and an optimized user experience."}, "auth-register": {"title": "Register Account ", "description": "Create your Safe Trade account in just a few steps and unlock a world of secure, transparent, and rewarding crypto trading."}, "blog": {"title": "SafeTrade Blog", "description": "Follow the real-time updates and trends of the world's leading cryptocurrency exchange"}, "announcements": {"title": "SafeTrade Announcement", "description": "Stay updated with the latest news and announcements from Safe Trade"}, "wallet-overview": {"title": "Wallet Overview"}, "wallet-spot-assets": {"title": "Wallet Spot Assets"}, "wallet-spot-withdraw": {"title": "Wallet Spot Withdraw"}, "wallet-spot-deposit": {"title": "Wallet Spot Deposit"}, "wallet-spot-deposit-record": {"title": "Wallet Spot Deposit Record"}, "wallet-spot-withdraw-record": {"title": "Wallet Spot Withdraw Record"}, "wallet-spot-transfer-history": {"title": "Wallet Spot Transfer History"}, "wallet-margin-assets": {"title": "<PERSON><PERSON>"}, "wallet-margin-record": {"title": "<PERSON><PERSON> Record"}, "wallet-financial-assets": {"title": "Wallet Financial Assets"}, "wallet-financial-record": {"title": "Wallet Financial Record"}, "wallet-amm-assets": {"title": "Wallet AMM Assets"}, "wallet-amm-income-records": {"title": "Wallet AMM Income Records"}, "wallet-amm-records": {"title": "Wallet AMM Records"}, "orders-open": {"title": "Orders Open"}, "orders-history": {"title": "Orders History"}, "orders-trades": {"title": "Orders Trades"}, "account-overview": {"title": "Account Overview"}, "account-security": {"title": "Account Security"}, "account-preferences": {"title": "Account Preferences"}, "account-api-management": {"title": "Account API Management"}, "account-verify": {"title": "Account <PERSON><PERSON><PERSON>"}, "account-address-book": {"title": "Account Address Book"}, "account-fees-trading": {"title": "Account Fees Trading"}, "account-fees-transaction": {"title": "Account Fees Transaction"}, "account-interactive-messages": {"title": "Account Interactive Messages"}, "account-setting": {"title": "Account <PERSON>ting"}, "account-snapshots": {"title": "Account <PERSON><PERSON><PERSON>s"}, "account-confirm-code": {"title": "Confirm Code"}}, "confirmCode": {"form": {"title": "Confirm Code", "description": "Please enter the code sent to your email to confirm your account.", "getCode": "Get Code", "getCodeAgain": "Get Code Again", "confirm": "Confirm", "enterEmailCode": "Enter Code", "codeSent": "Code sent to your email successfully", "codeConfirmationFailed": "Code confirmation failed", "codeConfirmed": "Code confirmed successfully", "emailRequired": "Email is required"}}, "blog": {"title": "SafeTrade Blog", "description": "Follow the real-time updates and trends of the world's leading cryptocurrency exchange", "followUs": "Follow Us", "shareThisArticle": "Share This Article", "readMore": "Read More", "loadMore": "Load More", "hotTopics": "🔥 Hot Topics", "categories": {"title": "Categories", "featured": "Featured"}, "telegram": {"title": "Join <PERSON><PERSON> for deeper market insights", "description": "Connect with our community for real-time market analysis and trading insights", "joinNow": "Join Now"}, "publishedOn": "Published on", "all": "All"}, "announcements": {"newListing": "New Listing", "more": "More"}, "breadcrumb": {"home": "Home", "markets-coin": "Markets", "markets-data": "Market Data", "exchange": "Exchange", "swap": "<PERSON><PERSON><PERSON>", "wallet-overview": "Wallet", "auth-login": "<PERSON><PERSON>", "auth-register": "Register", "account-confirm-code": "Confirm Code"}}