{"home": {"hero": {"title": "Dünyanın En Büyük Kripto Borsası", "description": "Kripto Alım Satımınızın Uzmanı", "buttonTrade": "<PERSON><PERSON>", "buttonRegister": "<PERSON><PERSON>"}, "ranking": {"title": "Sıralamalar", "hot": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "gainers": "Kazananlar", "losers": "<PERSON><PERSON><PERSON><PERSON>", "movers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "volume": "Hacim", "more": "<PERSON><PERSON>", "coins": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>(USD)", "change": "24H Değişim", "high": "24h <PERSON><PERSON><PERSON><PERSON>", "low": "24h Düşük"}, "breaking": {"title": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON>", "announcements": "<PERSON><PERSON><PERSON><PERSON>", "news": "<PERSON><PERSON><PERSON>", "insight": "İçgörüler", "minutes": "<PERSON><PERSON><PERSON>", "hours": "saat önce"}, "figures": {"title": "SafeTrade in Figures", "coins": "<PERSON><PERSON><PERSON>", "markets": "<PERSON><PERSON><PERSON><PERSON>", "value": "24-<PERSON><PERSON>(USD)", "value30": "30-<PERSON><PERSON><PERSON>(USD)"}, "download": {"title": "Kripto Alım Satımı. Her Zaman. Her Yerde. ", "description": "Binlerce varlık elinizde", "description2": "H<PERSON><PERSON>lı ve zamanında piyasa verileri", "description3": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON>", "qr": "SafeTrade Uygulamasını İndir"}, "services": {"heading": "Kullanıcıların dikkatine alan kripto alım satımı", "title": "Daha Fazla Hizmet Keşfet", "description": "200+ ü<PERSON><PERSON>/bö<PERSON> i<PERSON> des<PERSON> diller", "description2": "Başlangıç seviyesi özellikleri"}, "getToken": {"title": "SafeTrade'in CET token'ını alın", "description": "CET, dünyanın en büyük kripto borsası SafeTrade'in yerel token'ıdır.", "latestPrice": "<PERSON>(USD)", "totalIssued": "Toplam Çıkarılan(CET)", "circulatingSupply": "Çevrimiçi Arz(CET)", "burntRatio": "<PERSON><PERSON><PERSON><PERSON>"}, "tradeNow": {"title": "<PERSON><PERSON>", "description": "Kripto Alım Satımı. Her Zaman. Her Yerde.", "button": "<PERSON><PERSON>", "button2": "<PERSON><PERSON>", "buttonTrade": "<PERSON><PERSON>", "buttonRegister": "<PERSON><PERSON>"}}, "addressBook": {"title": "<PERSON><PERSON>", "addAddress": "<PERSON><PERSON>", "addAddressBook": "<PERSON><PERSON>", "addAddressBookSuccess": "Adres Defteri Başarıyla Eklendi", "addAddressBookFailed": "Adres Defteri Ekleme Başarısız", "currency": "Para Birimi", "address": "<PERSON><PERSON>", "network": "Ağ", "addressLabel": "<PERSON><PERSON>", "actions": "İşlemler", "withdraw": "Çek", "remove": "Sil", "deleteAddressBookSuccess": "<PERSON><PERSON> Başarı<PERSON>", "deleteAddressBookFailed": "Adres Defteri Silme Başarısız", "addAddressBookDescription": "<PERSON>ni bir adres defteri ekleyin", "enterEmailCode": "<PERSON><PERSON> kodu girin", "enterVerificationCode": "Do<PERSON><PERSON><PERSON>a kodu girin", "resendCode": "<PERSON><PERSON> tekrar g<PERSON>", "sendCode": "<PERSON><PERSON>", "enterCode2FA": "2FA kodu girin", "verify": "<PERSON><PERSON><PERSON><PERSON>"}, "apiManagement": {"title": "API Yönetimi", "apiKey": "API Anahtarı", "apiSecret": "API Gizli Anahtarı", "apiKeyDescription": "API Anahtarı, API isteklerinizi doğrulamak için kullanılır", "apiSecretDescription": "API Gizli Anahtarı, API isteklerinizi doğrulamak için kullanılır", "label": "Etiket", "status": "Durum", "actions": "İşlemler", "edit": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Sil", "apiKeyActive": "API Anahtarı aktif ve kullanılabilir", "apiKeyDisabled": "API Anahtarı devre dışı ve kullanılamaz", "createAPIKey": "API Anahtarı Oluştur", "editAPIKey": "API Anahtarı Düzenle", "updateAPIKeySettings": "API anahtarı ayarlarınızı güncelleyin", "createAPIKeyDescription": "Yeni bir API anahtarı oluşturun", "enterAPIKeyLabel": "API anahtarı etiketini girin", "permissions": "<PERSON><PERSON><PERSON>", "trustedIPAddresses": "Güvenilir IP Adresleri", "enterIPAddresses": "Virgül ile ayrılmış IP adreslerini girin (örn. ***********, ********)", "securityVerificationRequired": "Güvenlik Doğrulama Gerekli", "enter2FA": "Lütfen 2FA kodunuzu girin", "twoFactorAuthentication": "İki Faktörlü Doğrulama (2FA)", "updateAPIKey": "<PERSON><PERSON><PERSON><PERSON>", "apiKeyUpdated": "API Anahtarı Başarıyla Güncellendi", "apiKeyCreated": "API Anahtarı Başarıyla Oluşturuldu", "apiKeySaved": "Lütfen API kimlik bilgilerinizi güvenli bir şekilde kaydedin. Gizli anahtarı tekrar göremeyeceksiniz.", "apiKeyID": "API Anahtarı ID", "secretKey": "<PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON>", "failedToCopyToClipboard": "Kopyalama Başarısız", "copiedToClipboard": "{label} kopyalandı", "fillAllRequiredFields": "Lütfen tüm gerekli alanları doğru bir şekilde do<PERSON>", "fillAllFields": "Lütfen tüm alanları doldurun", "withdraw": "Çek", "withdrawDescription": "Fon çekme izin ver (IP kısıtlaması gerekiyor)", "addAPIKey": "API Anahtarı Ekle", "next": "<PERSON><PERSON><PERSON>", "deleteAPIKey": "API Anahtarı Sil", "deleteAPIKeyDescription": "Bu işlem geri alınamaz. API anahtarı kalıcı olarak kaldırılacak.", "apiKeyDeletedSuccessfully": "API Anahtarı Başarıyla Silindi", "deleteAPIKeyConfirmation": "Bu API anahtarını silmek istediğinize emin misiniz?", "deleteAPIKeyConfirmationDescription": "Bu işlem geri alınamaz. API anahtarı kalıcı olarak kaldırılacak."}, "fees": {"tradingFees": "Alım Satım Ücretleri", "fees": "<PERSON><PERSON><PERSON>", "marketID": "Piyasa ID", "maker": "Maker", "taker": "Taker", "search": "Ara", "transactionFees": "İşlem Ücretleri", "coinToken": "Coin/Token", "name": "Ad", "networks": "<PERSON><PERSON><PERSON>", "minDeposit": "<PERSON>", "depositFee": "<PERSON><PERSON><PERSON><PERSON>", "minWithdraw": "<PERSON>", "withdrawFee": "<PERSON><PERSON><PERSON>", "free": "Ücretsiz"}, "overview": {"accountID": "Hesap ID", "email": "Email", "accountIDDescription": "Hesap <PERSON>, <PERSON>daki benzersiz bir tanımlayıcıdır", "emailDescription": "Email, platformdaki birincil iletişim bilgileridir", "emailVerification": "<PERSON><PERSON>", "twoFactorAuthentication": "İki Faktörlü Doğrulama (2FA)", "set": "<PERSON><PERSON><PERSON>", "preferences": "<PERSON><PERSON><PERSON>", "open": "Aç", "verify": "<PERSON><PERSON><PERSON><PERSON>", "enabled": "Aktif", "notEnabled": "Devre Dışı", "verified": "Doğrulandı", "unverified": "Doğrulanmadı", "loginStatusManagement": "<PERSON><PERSON><PERSON>", "device": "Cihaz", "ipAddress": "IP Adresi", "time": "Zaman", "state": "Durum", "action": "İşlem", "online": "Çevrimiçi", "sessionDeletedSuccessfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>i"}, "interactiveMessages": {"title": "İletişim <PERSON>", "description": "İletişim <PERSON>", "button": "<PERSON><PERSON>", "markAllAsRead": "Tümünü Okundu Olarak İşaretle", "all": "Tümü", "read": "Okundu", "unread": "Okunmadı"}, "preferences": {"title": "<PERSON><PERSON><PERSON>", "orderConfirmation": "Sipariş Onayı", "spotTrading": "Spot Alım Satımı", "futuresTrading": "Futures Alım Satımı", "useCETAsFees": "CET'i Ücret olarak kullan", "payWithdrawalFee": "<PERSON><PERSON><PERSON>", "currency": "Para Birimi", "language": "Dil", "darkMode": "<PERSON><PERSON>", "smsCode": "SMS Kodu", "emailSubscriptions": "E-posta <PERSON>i", "listingAnnouncements": "<PERSON><PERSON><PERSON>", "latestPromotions": "<PERSON>", "blog": "Blog", "orderConfirmationDescription": "Siparişlerinizi verdiğinizde sipariş detaylarıyla birlikte bir pop-up onayı almak için etkinleştirin.", "set": "<PERSON><PERSON><PERSON>", "coin": "Coin", "usd": "USD", "english": "İngilizce", "vietnamese": "Vietnamca", "chinese": "<PERSON><PERSON><PERSON>", "useCETAsFeesDescription": "CET'i Ücret olarak kullan'ı etkinleştirmek, her işlem için (20%) alım satım ücreti ödemek için kullanılacaktır.", "payWithdrawalFeeDescription": "Normal çekimler için ödeme özelleştirme yapabilirsiniz", "smsCodeDescription": "<PERSON><PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON>ğ<PERSON>e, doğrulama kodları yalnızca SMS üzerinden gönderilecek ve diğer üçüncü taraf platformlardan gönderilmeyecektir.", "emailSubscriptionsDescription": "Aşağıdaki kategorilerde e-posta bildirimleri almak için etkinleştirin.", "listingAnnouncementsDescription": "Aşağıdaki kategorilerde e-posta bildirimleri almak için etkinleştirin.", "latestPromotionsDescription": "Aşağıdaki kategorilerde e-posta bildirimleri almak için etkinleştirin."}, "security": {"title": "Güvenlik Ayarları", "withdrawalPassword": "<PERSON><PERSON><PERSON>", "withdrawalPasswordDescription": "Çekim şifresini ayarlayarak varlıklarınızı koruyun", "tradingPassword": "<PERSON>ım Satım Şifresi", "tradingPasswordDescription": "<PERSON>ım satım şifresini ayarlayarak işlemlerinizi güvenle yapın", "antiPhishingCode": "Anti-Phishing <PERSON>", "antiPhishingCodeDescription": "SafeTrade'in resmi e-postalarını doğrulamak için", "withdrawalMultiApproval": "<PERSON><PERSON><PERSON>", "withdrawalMultiApprovalDescription": "Hesabı yönetmek için çekim onay e-postası ekleyin", "logInWithThirdPartyAccount": "Üçüncü Taraf <PERSON> ile Giriş Yap", "logInWithThirdPartyAccountDescription": "SafeTrade'e üçünc<PERSON> taraf hesabı ile hızlı giriş yapın", "lockLoginIP": "Giriş IP Kilit", "lockLoginIPDescription": "<PERSON><PERSON><PERSON>leş<PERSON>rildiğinde, web üzerindeki giriş oturumu yeni bir IP adresine geçiş yapıldığında sonlanacaktır.", "secureLoginDuration": "Güven<PERSON>", "secureLoginDurationDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, web üzerindeki giriş oturumu bir saat işlem yapmadan sonra sonlanacaktır.", "accountManagement": "<PERSON><PERSON><PERSON>", "accountManagementDescription": "<PERSON><PERSON><PERSON><PERSON> dondurun veya silin", "active": "Aktif", "manage": "<PERSON><PERSON><PERSON>", "notSet": "Ayarlanmamış", "toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set": "<PERSON><PERSON><PERSON>", "email": "E-posta", "password": "Şifre", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "basicSettings": "<PERSON><PERSON>", "changeEmail": "E-posta <PERSON>r", "changePassword": "<PERSON><PERSON><PERSON>", "passkey": "Pa<PERSON><PERSON>", "passkeyDescription": "Hesabınızı korumak için bir güvenlik cihazı kullanın, <PERSON><PERSON><PERSON><PERSON>.", "totpVerification": "TOTP Doğrulama", "totpVerificationDescription": "Hesabınızı korumak için TOTP doğrulayıcı kullanın.", "mobile": "Mobil", "mobileDescription": "Hesabınızı korumak için SMS doğrulaması kullanın.", "enable": "Etkinleştir", "disable": "Devre Dışı Bırak", "enabled": "Aktif", "twoFactorAuthentication": "İki Faktörlü Doğrulama (2FA)"}, "snapshots": {"title": "Anlık Görüntü", "createSnapshot": "Anlık Görüntü Oluştur", "type": "Tip", "status": "Durum", "timeRange": "Zaman Aralığı", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "<PERSON><PERSON><PERSON>", "actions": "İşlemler", "download": "<PERSON><PERSON><PERSON>", "downloadSuccess": "Anlık görüntü başarıyla indirildi", "createSuccess": "Anlık görüntü başarıyla oluşturuldu", "transactions": "İşlemler", "trades": "İşlemler", "accounts": "<PERSON><PERSON><PERSON><PERSON>", "allTime": "<PERSON><PERSON><PERSON>", "createSnapshotDescription": "Hesap verilerinizin yeni bir anlık görüntüsünü oluşturun", "selectSnapshotType": "Anlık görüntü tipi seçin", "selectTimeRange": "Zaman aralığı seçin", "year": "<PERSON><PERSON><PERSON>", "selectYear": "<PERSON><PERSON><PERSON>", "dateRange": "<PERSON><PERSON><PERSON>", "selectDateRange": "<PERSON><PERSON><PERSON>", "enterCodeTwoFactor": "İki Faktörlü Doğrulama (2FA) kodunu girin", "next": "<PERSON><PERSON><PERSON>", "pleaseSelectSnapshotType": "Lütfen bir anlık görüntü tipi seçin", "pleaseSelectTimeRange": "Lütfen bir zaman aralığı seçin", "pleaseSelectYear": "Lütfen bir yıl se<PERSON>in", "pleaseSelectDateRange": "Lütfen bir tarih a<PERSON>ığı se<PERSON>in", "pleaseFillInAllRequiredFields": "Lütfen tüm gerekli alanları doldurun", "enterCodeTwoFactorDescription": "İşlemi onaylamak için doğrulayıcı uygulamasından kodu girin", "past7Days": "Son 7 gün", "past30Days": "Son 30 gün", "past90Days": "Son 90 gün", "past12Months": "Son 12 ay", "aCalendarYear": "Bir takvim yılı", "customize": "Ö<PERSON>leş<PERSON>r"}, "verify": {"title": "Kimlik Doğrulama", "howToVerify": "Na<PERSON>ıl <PERSON>ı<PERSON>", "iPromise": "Sağlanan bilgiler benimle ilgili ve diğer insanların bilgileri kopyalanmamıştır.", "iAmAllSet": "ID Doğrulama için ha<PERSON>ı<PERSON>m", "basicInfo": "<PERSON><PERSON>", "uploadId": "<PERSON>", "reminder1": "Lütfen sağlanan bilgilerinizin ID bilgilerinize eşleştiğinden emin olun ve bu bilgiler ID doğrulama onaylandıktan sonra değiştirilemez.", "reminder2": "Kamera cihazınızda yüklü <PERSON>ğ<PERSON>e, ID doğrulama için SafeTrade App kullanın.", "reminder3": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ABD Küçük Dış Adaları, Hong Kong, <PERSON><PERSON>, Kanada'da<PERSON> k<PERSON> için Kimlik Doğrulamasını desteklememektedir.", "reminder4": "Denetim sonuçları 1-3 iş günü içinde e-postanıza gönderilecektir. Lütfen posta kutunuzu kontrol edin.", "withdrawQuota": "24 saatlik çekim kotası: {quota}", "withdrawQuotaDescription": "Kimlik doğrulamasını geçtikten sonra aşağıdaki ayrıcalıklara sahip olacaksınız:"}, "popup": {"twoFactor": {"disable": "2FA'yı Devre Dışı Bırak", "disableDescription": "2FA'yı devre dışı bırakmak için lütfen e-posta kodunu ve kimlik doğrulayıcı uygulamanızdaki kodu girin.", "enterEmailCode": "E-posta kodunu girin", "enterCodeTwoFactor": "İki Faktörlü Doğrulama (2FA) kodunu girin", "didntGetCode": "Kodu almadınız mı?", "getCode": "<PERSON><PERSON> al", "codeSentSuccessfully": "<PERSON>d başarı<PERSON> gö<PERSON>il<PERSON>", "twoFactorDisabledSuccessfully": "2FA başarıyla devre dışı bırakıldı", "emailCodeRequired": "E-posta kodu gere<PERSON>li", "otpRequired": "OTP gerekli", "totpSettings": "TOTP Ayarları", "enterVerificationCode": "Do<PERSON><PERSON><PERSON>a kodu girin", "resendCode": "<PERSON><PERSON> tekrar g<PERSON>", "sendCode": "<PERSON><PERSON>", "privateKey": "<PERSON><PERSON>", "step1": "1. Google Authenticator gibi TOTP Kimlik Doğrulayıcı Uygulamalarını indirin ve yükleyin.", "step2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, \"+\" veya \"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"ye tıklayın ve \"QR kodu tara\"yı se<PERSON><PERSON>.", "note": "Not: <PERSON><PERSON><PERSON>, <PERSON><PERSON>ı<PERSON>ız <PERSON>ğiştiğinde veya kaybolduğunda TOTP'yi geri yüklemek için kullanılabilir, lütfen anahtarı güvenli bir şekilde saklayın. <PERSON><PERSON>, varl<PERSON>k güvenliğiniz için CoinEx anahtar kurtarma hizmeti sunmaz.", "emailVerification": "E-posta doğrulama", "verificationCode": "Doğrulama kodu", "successfully": "Başarılı", "pleaseEnterCodeSentToYourEmail": "Lütfen e-postanıza gönderilen kodu girin", "totpSettingsSuccessfully": "TOTP ayarları başarıyla tamamlandı", "invalidVerificationEmailCodeOr2faCode": "Geçersiz doğrulama e-posta kodu veya 2FA kodu", "secretKeyCopiedToClipboard": "Gizli anahtar panoya kopyalandı", "passkeyAddedSuccessfully": "Parola başarıyla eklendi", "next": "<PERSON><PERSON><PERSON>", "verificationCodePlaceholder": "XXX-XXX", "emailVerificationDescription": "2FA'yı etkinleştirmek için lütfen e-posta kodunu ve kimlik doğrulayıcı uygulamanızdaki kodu girin."}, "updateEmail": {"title": "E-posta <PERSON>r", "description": "Lütfen e-postanıza gönderilen 6 haneli doğrulama kodunu girin", "enterEmailCode": "E-posta kodunu girin", "enterVerificationCode": "<PERSON><PERSON><PERSON><PERSON>a kodunu girin", "enterNewEmail": "Yeni e-posta girin", "enterNewEmailCode": "Yeni e-posta kodunu girin", "changeEmail": "E-posta <PERSON>r", "pleaseVerifyEmailToChangeEmail": "E-postayı değiştirmek için lütfen e-postanızı doğrulayın", "verificationCodeSentToYourCurrentEmail": "Mevcut e-postanıza doğrulama kodu gönderildi", "pleaseEnterYourNewEmail": "Lütfen yeni e-postanızı girin", "verificationCodeSentToYourNewEmail": "Yeni e-postanıza doğrulama kodu gönderildi", "emailChangedSuccessfully": "E-posta başarıyla değiştirildi"}, "updatePassword": {"title": "<PERSON><PERSON><PERSON>", "description": "Lütfen e-postanıza gönderilen 6 haneli doğrulama kodunu girin", "enterEmailCode": "E-posta kodunu girin", "enterVerificationCode": "<PERSON><PERSON><PERSON><PERSON>a kodunu girin", "enterNewEmail": "Yeni e-posta girin", "enterNewEmailCode": "Yeni e-posta kodunu girin", "changeEmail": "<PERSON><PERSON><PERSON>", "pleaseVerifyEmailToChangeEmail": "Şifreyi değiştirmek için lütfen e-postanızı doğrulayın", "verificationCodeSentToYourCurrentEmail": "Mevcut e-postanıza doğrulama kodu gönderildi", "pleaseEnterYourNewEmail": "Lütfen yeni şifrenizi girin", "verificationCodeSentToYourNewEmail": "Yeni şifreniz için doğrulama kodu e-postanıza gönderildi", "emailChangedSuccessfully": "<PERSON><PERSON>re b<PERSON>şarıyla değiştirildi"}, "updateUsername": {"title": "Kullanıcı adını değiştir", "description": "Lütfen yeni kullanıcı adınızı girin", "enterNewUsername": "Yeni kullanıcı adını girin", "enterUsername": "Kullanıcı adını girin", "changeUsername": "Kullanıcı adını değiştir", "usernameChangedSuccessfully": "Kullanıcı adı başarıyla değiştirildi", "usernameChangeFailed": "Kullanıcı adı değiştirme başarısız oldu"}, "termsOfSafeTradeService": "SafeTrade Hizmet Şartları", "byRegisteringAnAccount": "Bir <PERSON><PERSON><PERSON>, şunları kabul etmiş olursunuz", "login": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Lütfen hesabınızla giriş yapın veya SafeTrade Uygulaması ile QR kodu tarayın", "signIn": "<PERSON><PERSON><PERSON> yap"}, "register": {"title": "<PERSON><PERSON>", "subtitle": "Kripto Dünyasını Keşfetmeye Başla", "signUp": "<PERSON><PERSON><PERSON>"}, "pleaseEnterYourAccount": "Lütfen hesabınızı girin", "pleaseEnterYourPassword": "Lütfen şifrenizi girin", "passwordDoesNotMatch": "<PERSON><PERSON><PERSON> e<PERSON>şmiyor", "loginSuccessfully": "Başarıyla giriş yapıldı", "confirmCodeSuccessfully": "Kod başarıyla onaylandı", "pleaseCheckYourEmailToGetTheCode": "Kodu almak için lütfen e-postanızı kontrol edin", "resetSuccessfully": "Sıfırlama başarılı", "email": "Email", "newPassword": "<PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "enterCode": "<PERSON><PERSON> girin", "resetPassword": {"title": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitle": "Yeni şifre en az 8 karakter uzunluğunda olmalı ve 1 harf, 1 özel karakter ve 1 sayı içermelidir.", "button": "<PERSON><PERSON><PERSON> s<PERSON>"}, "securityAuthentication": {"title": "Güvenlik Doğrulaması", "subtitle": "Lütfen Kimlik Doğrulayıcı'daki 6 haneli OTP kodunu girin", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "signIn": "<PERSON><PERSON><PERSON> yap", "logInWithQRCode": "QR kodu ile giriş yap", "enterHomeInAppAndScanInTheUpperRightCornerToLogInViaQRCode": "Uygulamada \"Ana Sayfa\"ya girin ve QR kodu ile giriş yapmak için sağ üst köşedeki \"Tara\" uygulamasını kullanın", "registerNow": "<PERSON><PERSON><PERSON>", "forgotPassword": "Şif<PERSON><PERSON>", "alreadyRegistered": "Zaten kayıtlı mısınız?", "logInNow": "Şim<PERSON> G<PERSON>", "account": "<PERSON><PERSON><PERSON>", "password": "Şifre", "enterAccount": "<PERSON><PERSON><PERSON> girin", "enterPassword": "<PERSON><PERSON><PERSON><PERSON> girin", "enterConfirmPassword": "<PERSON><PERSON><PERSON><PERSON>"}, "auth": {"banner": {"title": "Bugün bizimle işlem yapın", "description": "Daha Fazla Güvenlik 🛡️ Daha İyi Gizlilik. Daha Az Stres.", "feature1": "Al ve sat", "feature2": "<PERSON><PERSON><PERSON> ben<PERSON>e i<PERSON> ya<PERSON>.", "feature3": "<PERSON>ket halindeyken işlem yapın. Her yerde, her zaman.", "feature4": "Kullanıcılarımızı İlk Sıraya Koyuyoruz"}, "continueWithOtherLogin": "<PERSON><PERSON><PERSON> g<PERSON> de<PERSON> et", "termsOfSafeTradeService": "SafeTrade Hizmet Şartları", "byRegisteringAnAccount": "Bir <PERSON><PERSON><PERSON>, şunları kabul etmiş olursunuz", "login": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Lütfen hesabınızla giriş yapın veya SafeTrade Uygulaması ile QR kodu tarayın", "signIn": "<PERSON><PERSON><PERSON> yap"}, "register": {"title": "<PERSON><PERSON>", "subtitle": "Kripto Dünyasını Keşfetmeye Başla", "signUp": "<PERSON><PERSON><PERSON>"}, "pleaseEnterYourAccount": "Lütfen hesabınızı girin", "pleaseEnterYourPassword": "Lütfen şifrenizi girin", "passwordDoesNotMatch": "<PERSON><PERSON><PERSON> e<PERSON>şmiyor", "loginSuccessfully": "Başarıyla giriş yapıldı", "confirmCodeSuccessfully": "Kod başarıyla onaylandı", "pleaseCheckYourEmailToGetTheCode": "Kodu almak için lütfen e-postanızı kontrol edin", "resetSuccessfully": "Sıfırlama başarılı", "email": "Email", "newPassword": "<PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "enterCode": "<PERSON><PERSON> girin", "resetPassword": {"title": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitle": "Yeni şifre en az 8 karakter uzunluğunda olmalı ve 1 harf, 1 özel karakter ve 1 sayı içermelidir.", "button": "<PERSON><PERSON><PERSON> s<PERSON>"}, "securityAuthentication": {"title": "Güvenlik Doğrulaması", "subtitle": "Lütfen Kimlik Doğrulayıcı'daki 6 haneli OTP kodunu girin", "button": "<PERSON><PERSON><PERSON><PERSON>"}, "logout": "Çıkış Yap", "enterEmail": "<PERSON><PERSON> girin", "signIn": "<PERSON><PERSON><PERSON> yap", "logInWithQRCode": "QR kodu ile giriş yap", "enterHomeInAppAndScanInTheUpperRightCornerToLogInViaQRCode": "Uygulamada \"Ana Sayfa\"ya girin ve QR kodu ile giriş yapmak için sağ üst köşedeki \"Tara\" uygulamasını kullanın", "registerNow": "<PERSON><PERSON><PERSON>", "forgotPassword": "Şif<PERSON><PERSON>", "alreadyRegistered": "Zaten kayıtlı mısınız?", "logInNow": "Şim<PERSON> G<PERSON>", "account": "<PERSON><PERSON><PERSON>", "password": "Şifre", "enterAccount": "<PERSON><PERSON><PERSON> girin", "enterPassword": "<PERSON><PERSON><PERSON><PERSON> girin", "enterConfirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "securityEmail": {"title": "E-posta Doğrulama", "subtitle": "Lütfen e-postanıza gönderilen 6 haneli doğrulama kodunu girin", "button": "<PERSON><PERSON><PERSON>"}}, "contact": {"title": "Bize Ulaşın", "subtitle": "Daha fazla işbirliği fırsatı için SafeTrade'e katılın", "moreCooperation": "Daha Fazla İşbirliği", "followUs": "Bizi Takip Ed<PERSON>", "copied": "Kopyalandı", "toClipboard": "<PERSON><PERSON>", "mediaAndCooperation": "Me<PERSON>a ve İşbirliği", "productAndSupport": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>k", "vulnerabilityReport": "Güvenlik Açığı Raporu", "lawEnforcement": "<PERSON><PERSON><PERSON>"}, "exchange": {"announcements": "<PERSON><PERSON><PERSON><PERSON>", "support": "Destek", "orderBook": "<PERSON><PERSON>", "24hChange": "24s <PERSON><PERSON><PERSON><PERSON><PERSON>", "24hHigh": "24s <PERSON><PERSON><PERSON><PERSON>", "24hLow": "24s <PERSON><PERSON><PERSON><PERSON><PERSON>", "24hAmount": "24s miktar", "24hVolume": "24s hacim", "amount": "<PERSON><PERSON><PERSON>", "sum": "Toplam", "buy": "Al", "sell": "Sat", "limit": "Limit", "market": "<PERSON><PERSON><PERSON>", "stopLimit": "Stop Limit", "currency": "Para Birimi", "price": "<PERSON><PERSON><PERSON>", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON>", "toTrade": "işlem yapmak için", "marketTrades": "<PERSON><PERSON><PERSON>", "myTrades": "İşlemlerim", "time": "Zaman", "priceMustBeGreaterThan": "Fiyat şundan büyük olmalı", "priceMustBeLessThan": "Fiyat şundan küçük olmalı", "amountMustBeGreaterThan": "<PERSON><PERSON>ar <PERSON>n büyük olmalı", "enableSlippage": "Kayma Ayarı Etkinleştir", "setSlippage": "Kayma <PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "total": "Toplam", "stop": "<PERSON><PERSON><PERSON>", "available": "Mevcut", "averagePrice": "Ortalama <PERSON>", "tradingView": "TradingView", "depth": "<PERSON><PERSON><PERSON>"}, "markets": {"coin": {"recommendedRanks": "<PERSON><PERSON><PERSON>n <PERSON>", "valueLeaders": "<PERSON><PERSON><PERSON> Li<PERSON>leri", "top24hVolume": "En Yüksek 24s Hacim", "topGainers": "En Çok Kazananlar", "topLosers": "En Çok <PERSON>bedenler", "cryptoList": "<PERSON><PERSON><PERSON>", "favorites": "<PERSON><PERSON><PERSON><PERSON>", "topVolume": "En Yüksek Hacim", "coin": "Kripto Para", "lastPrice": "<PERSON>(USD)", "24hChange": "24s <PERSON><PERSON><PERSON><PERSON><PERSON>", "24hHigh": "24s <PERSON><PERSON><PERSON><PERSON>", "24hLow": "24s Düşük", "24hVolume": "24s <PERSON><PERSON><PERSON>", "action": "İşlem", "updatedFavoriteSuccessfully": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "trade": "<PERSON><PERSON>m <PERSON>ım"}, "data": {"backToMarkets": "<PERSON><PERSON><PERSON><PERSON>", "listedCrypto": "<PERSON><PERSON><PERSON>", "coins": "<PERSON><PERSON><PERSON>", "24hPriceChange": "24 Saatlik Fiyat Değişimi", "priceUp": "<PERSON><PERSON><PERSON>", "priceDown": "Fiyat Düşüşü", "unchanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "totalMarketCap": "Toplam Piyasa <PERSON> (USD)", "24hSpotTradingValue": "24 Saatlik Spot İşlem Değeri (USD)", "topChange": "En Çok Değişen", "topVolume": "En Yüksek Hacim", "historicalMarketValue": "Geçmiş Piyasa <PERSON>", "unit": "<PERSON><PERSON><PERSON>", "orderDistribution": "<PERSON><PERSON>", "takerBuy": "Alıcı Alışı", "takerSell": "Alıcı Satışı", "priceChangeDistribution": "<PERSON>yat <PERSON>ğ<PERSON>şimi Dağılımı", "topGainers": "En Çok Kazananlar", "topLosers": "En Çok <PERSON>bedenler", "valueLeaders": "<PERSON><PERSON><PERSON> Li<PERSON>leri", "topMarketCap": "En Yüksek Piyasa <PERSON>ğeri", "topCoins": "En İyi Kripto Paralar", "viewAll": "<PERSON>ümü<PERSON><PERSON>", "coin": "Kripto Para", "price": "<PERSON><PERSON><PERSON>", "24hChange": "24 Saatlik Değişim", "cryptoHeatMap": "Kripto Isı Haritası", "recommendedRanks": "<PERSON><PERSON><PERSON>n <PERSON>"}}, "confirmCode": {"form": {"title": "Ko<PERSON> Doğrula", "description": "Lütfen kodu girin", "getCode": "Kodu Al", "getCodeAgain": "Kodu Tekrar Al", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "enterEmailCode": "E-posta kodunu girin", "codeSent": "E-posta adresinize kod gönderildi", "codeConfirmationFailed": "Kod doğrulama başarısız oldu", "codeConfirmed": "Kod doğrulandı", "emailRequired": "E-posta adresi gereklidir"}}, "orders": {"market": "<PERSON><PERSON><PERSON>", "allMarket": "<PERSON><PERSON><PERSON>", "type": "Tip", "allType": "<PERSON><PERSON><PERSON>", "side": "<PERSON><PERSON><PERSON>", "allSide": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "or": "veya", "register": "<PERSON><PERSON><PERSON>", "toTrade": "işlem yapmak için", "state": "Durum", "avgPrice": "Ort. <PERSON>", "filled": "Do<PERSON>", "action": "İşlem", "amount": "<PERSON><PERSON><PERSON>", "total": "Toplam", "orderCanceledSuccessfully": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> iptal edildi", "orderCancelFailed": "<PERSON><PERSON> i<PERSON> başarısız oldu", "cancel": "İptal", "fee": "Ücret", "spotOrder": "Spot Emir", "open": "Açık", "history": "Geçmiş", "trades": "İşlemler", "price": "<PERSON><PERSON><PERSON>", "limit": "Limit", "buy": "Al", "sell": "Sat"}, "support": {"faq": {"frequentlyAskedQuestions": "Sıkça Sorulan Sorular", "searchByKeywords": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AMM", "search": "Ara"}}, "swap": {"faq": "SSS", "viewAllFaqs": "Tüm SSS'le<PERSON>", "swap": "<PERSON><PERSON>", "swapAssetsEffortlesslyAndSecurely": "SafeTrade'in kendi geliştirdiği algoritma ile varlıkları zahmetsizce ve güvenli bir şekilde takas edin", "select": "Seç", "enterAmount": "<PERSON><PERSON><PERSON> girin", "to": "<PERSON><PERSON><PERSON>", "loginToSwap": "<PERSON><PERSON> <PERSON>", "referencePrice": "Referans Fiyatı", "pleaseSelectCurrency": "Lütfen bir para birimi seçin", "pleaseEnterAmount": "Lütfen bir miktar girin", "last5Records": "Son 5 Kayıt", "allRecords": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "direction": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "status": "Durum", "operation": "İşlem", "trendySwaps": "Trend <PERSON>"}, "notification": {"requireTOTPVerification": "TOTP Doğrulama Gerekli", "requireTOTPVerificationDescription": "<PERSON><PERSON><PERSON><PERSON>, devam etmeden önce TOTP'yi etkinleştirin.", "enableOTP": "TOTP Etkinleştir"}, "wallet": {"selectAll": "Tümünü Seç", "spotAssets": "Spot Varlıklar", "totalAssets": "Toplam Varlıklar", "available": "Mevcut", "locked": "<PERSON><PERSON><PERSON>", "selectFromAddressBook": "<PERSON><PERSON>", "network": "Ağ", "selectNetwork": "<PERSON>ğı Seç", "addWithdrawAddress": "<PERSON><PERSON><PERSON>", "safeTradeAccount": "SafeTrade Hesabı", "transferMode": "Transfer Modu", "address": "<PERSON><PERSON>", "assets": "Varlıklar", "time": "Zaman", "memo": "Memo", "date": "<PERSON><PERSON><PERSON>", "fee": "Ücret", "amount": "<PERSON><PERSON><PERSON>", "verifyOtp": "OTP Doğrula", "verifyOtpDescription": "Lütfen e-postanıza gönderilen 6 haneli doğrulama kodunu ve 2FA kodunu girin", "enterEmailCode": "E-posta kodunu girin", "enterVerificationCode": "<PERSON><PERSON><PERSON><PERSON>a kodunu girin", "enterCodeTwoFactorAuthentication": "İki Faktörlü Doğrulama (2FA) kodunu girin", "verify": "<PERSON><PERSON><PERSON><PERSON>", "resendCode": "<PERSON><PERSON> tekrar gö<PERSON> ({time}s)", "sendCode": "<PERSON><PERSON>", "notificationCodeSent": "Doğrulama kodu için lütfen e-postanızı kontrol edin", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortBy": "Şuna göre sırala", "allAssets": "<PERSON><PERSON>m Varlıklar", "export": "Dışa Aktar", "txFee": "İşlem Ücreti", "txid": "Txid", "depositAddress": "<PERSON><PERSON><PERSON><PERSON>", "credited": "Yatırıldı", "status": "Durum", "type": "Tip", "currency": "Para Birimi", "copiedAddressToClipboard": "Adres panoya kopyalandı", "estTotalAssets": "<PERSON><PERSON><PERSON>lam Varlıklar", "todaysPNL": "Bugünkü K/Z", "pnlAnalysisActive": "K/Z Analizi Aktif", "activatePNLAnalysis": "K/Z Analizini Etkinleştir", "buyViaP2P": "P2P Aracılığıyla Al", "buyViaThirdParty": "Üçüncü Taraf <PERSON>lığıyla Al", "assetPortfolio": "Varlık Portföyü", "search": "Ara", "hideSmallAssets": "Küçük Varlıkları Gizle", "smallAssetConverter": "Küçük varlık dönüştürücü", "assetsUnderMaintenance": "Bakımdaki Varlıklar", "coin": "Kripto Para", "total": "Toplam", "btcValue": "BTC Değeri", "actions": "İşlemler", "trade": "<PERSON><PERSON>m <PERSON>ım", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdraw": "Çek", "transfer": "Transfer", "depositHasBeenCreditedToYourAccount": "Yat<PERSON><PERSON> he<PERSON>ıza yatırıldı", "amountMustGreaterThan0": "Miktar 0'dan büyük olmalı", "depositFeeNotPaid": "<PERSON><PERSON><PERSON><PERSON>med<PERSON>", "youNeed": "İhtiyacınız var", "toProcessThisDeposit": "bu yatırmayı işlemek için", "youNeedToDepositAdditional": "Ek yatırma yapmanız gerekiyor", "depositBelowMinimumAmount": "Minimum miktarın altında yatırma", "depositIsBeingProcessed": "<PERSON><PERSON><PERSON><PERSON>lem<PERSON> devam ediyor", "last5DepositRecords": "Son 5 Yatırma <PERSON>", "allDeposits": "<PERSON><PERSON><PERSON>", "transferNetwork": "Transfer ağı", "selectTheNetwork": "<PERSON><PERSON><PERSON>", "depositDetails": "<PERSON><PERSON><PERSON><PERSON>", "attention": "<PERSON><PERSON><PERSON>", "depositFee": "<PERSON><PERSON><PERSON><PERSON>", "minimumDepositAmount": "Minimum Yatırma Miktarı", "confirmation": "Onay(lar)", "depositArrival": "<PERSON><PERSON><PERSON><PERSON>", "noDepositAddressFound": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON> bulu<PERSON>", "noNetworkFound": "<PERSON><PERSON> bulunamadı", "minConfirmations": "Minimum Onaylar", "contractAddress": "Sözleşme Adresi", "depositNote1": "Lütfen bu adrese yalnızca {currency} yatırması yapıldığından emin olun. <PERSON><PERSON><PERSON>, yatırdığınız fonlar bakiyenize eklenmeyecek ve iade edilmeyecektir.", "depositNote2": "Lütfen SafeTrade {currency} yatırma adresinizin doğru olduğundan emin olun. <PERSON><PERSON><PERSON>, yatırdığınız fonlar bakiyenize eklenmeyecek ve iade edilmeyecektir.", "note": "Not", "tradeNow": "Şimdi İşlem Yap", "retrievingUncreditedDeposit": "Alacaklandırılmamış Yatırmayı Geri Alma", "depositRecords": "<PERSON><PERSON><PERSON><PERSON>", "normalTransfer": "Normal Transfer", "internalTransfer": "Dahili Transfer (0 Ücret)", "withdrawToSafeTradeAccount": "Bir SafeTrade hesabına <PERSON>ek", "pleaseEnterSafetradeAccount": "Lütfen SafeTrade hesabını girin", "withdrawDetails": "<PERSON><PERSON><PERSON>", "withdrawalRequestSentSuccessfully": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>ö<PERSON>", "pleaseFillInAllRequiredFields": "Lütfen tüm gerekli alanları doldurun", "last5WithdrawRecords": "Son 5 Çekim <PERSON>", "allWithdrawals": "<PERSON><PERSON><PERSON>", "pleaseSelectNetworkFirst": "Lütfen önce bir ağ seçin", "amountMustBeGreaterThanMinimum": "<PERSON><PERSON><PERSON> büyük olmalı: {minWithdraw} {currency}", "insufficientBalance": "<PERSON><PERSON><PERSON> b<PERSON>. Maksimum mevcut: {maxBalance} {currency}", "insufficientBalanceForFee": "Ücret için yet<PERSON> bakiye ({fee} {currency}). Lütfen miktarı azaltın.", "pleaseEnterValidAddressAndChooseNetwork": "Lütfen geçerli bir adres girin ve ağ seçin", "invalidAddressForNetwork": "{network} ağı için geçersiz {currency} adresi", "couldNotValidateAddress": "Adres doğrulanamadı. Lütfen kontrol edin ve tekrar deneyin.", "pleaseEnterValidSafeTradeAccount": "Lütfen geçerli bir SafeTrade hesabı girin", "pleaseFillInAllFields": "Lütfen tüm alanları doldurun", "pleaseEnterOrSelectWithdrawalAddress": "Lütfen çekim adresini girin veya seçin", "addressBook": "<PERSON><PERSON>", "pleaseEnterYourMemoHere": "Lütfen memo'nuzu buraya girin", "pleaseEnterTheAmount": "Lütfen miktarı girin", "pleaseEnterYourWithdrawalNoteHere": "Lütfen çekim notunuzu buraya girin", "withdrawNote1": "Lütfen bu adrese yalnızca {currency} çekimi yapıldığından emin olun. <PERSON><PERSON><PERSON>, çektiğiniz fonlar bakiyenize eklenmeyecek ve iade edilmeyecektir.", "withdrawNote2": "Lütfen SafeTrade {currency} çekim adresinizin doğru olduğundan emin olun. <PERSON><PERSON><PERSON>, çektiğiniz fonlar bakiyenize eklenmeyecek ve iade edilmeyecektir.", "withdrawRecords": "<PERSON><PERSON><PERSON>", "withdrawAmount": "<PERSON><PERSON><PERSON>", "withdrawFee": "<PERSON><PERSON><PERSON>", "received": "Alındı", "assetOverview": "Varlık Genel Bakışı", "verified": "Doğrulandı", "unverified": "Doğrulanmadı", "financial": {"tabs": {"financial": "Finansal", "staking": "Staking"}, "stats": {"cmlIncomes": "<PERSON><PERSON><PERSON><PERSON><PERSON> (USD)", "lastDayIncome": "<PERSON><PERSON><PERSON> (USD)", "avgAPY": "Ortalama APY"}, "columns": {"cmlIncomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastDayIncome": "<PERSON><PERSON><PERSON>", "oneDayAPY": "1-Gün APY", "sevenDayAPY": "7-<PERSON><PERSON><PERSON>", "operation": "İşlem"}}, "amm": {"stats": {"totalIncome": "<PERSON><PERSON> Gelir (USDT)", "lastDayIncome": "<PERSON><PERSON><PERSON> (USDT)", "ammMarkets": "AMM Piyasaları"}, "showMyPairsOnly": "<PERSON><PERSON><PERSON>", "columns": {"pair": "Parite", "myLiquidity": "<PERSON><PERSON>", "totalLiquidity": "Toplam Likidite", "position": "Pozisyon", "sevenDayFeeGrowth": "7-<PERSON><PERSON><PERSON>üyümesi", "oneDayAPY": "1-Gün APY", "twoDayAPY": "2-Gün APY", "operation": "İşlem"}, "actions": {"add": "<PERSON><PERSON>", "remove": "Kaldır"}}}, "header": {"currency": "Para Birimi", "darkMode": "<PERSON><PERSON>", "logout": "Çıkış Yap", "login": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON>"}, "menu": {"buy-crypto": "Kripto Al", "p2p-trading": "P2P İşlemler", "third-party-trading": "Üçüncü Taraf <PERSON>", "markets": "<PERSON><PERSON><PERSON><PERSON>", "market-overview": "Piyasa Genel Bakışı", "market-data": "<PERSON><PERSON><PERSON>", "feed": "Akış", "insight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swap": "<PERSON><PERSON>", "spot": "Spot", "pre-token-trading": "Pre-token İşlemleri", "finance": "Finans", "staking": "Staking", "amm": "AMM", "loans": "<PERSON><PERSON><PERSON>", "promotion": "Promosyon", "trade-board": "İşlem Panosu", "deposit-bonus": "<PERSON><PERSON><PERSON><PERSON>", "mining": "Madencilik", "airdrop-station": "Airdrop Stasyonu", "more": "<PERSON><PERSON>", "referral": "Referans", "ambassador": "Ambasador", "academy": "Akademi", "help-center": "<PERSON><PERSON><PERSON>", "announcements": "<PERSON><PERSON><PERSON><PERSON>", "assets": "Varlıklar", "orders": "<PERSON><PERSON><PERSON>", "account-overview": "Hesap <PERSON>l Bakışı", "security-setting": "Güvenlik Ayarları", "id-verification": "Kimlik Doğrulama", "preferences": "<PERSON><PERSON><PERSON>", "address-book": "<PERSON><PERSON>", "fees": "<PERSON><PERSON><PERSON>", "trading-fees": "İşlem Ücretleri", "transaction-fees": "İşlem Ücretleri", "api-management": "API Yönetimi", "snapshots": "An<PERSON>ık <PERSON>", "overview": "Genel Bakış", "margin": "<PERSON><PERSON>", "financial": "Finans", "history": "Geçmiş", "open-order": "Açık Emir", "history-order": "Geçmiş Emirler", "trade-history": "İşlem Geçmişi", "spot-assets": "Spot Varlıklar", "spot-withdraw": "<PERSON>ekim", "spot-deposit": "Spot Yatırma", "spot-deposit-record": "Spot Yatırma <PERSON>ı", "spot-withdraw-record": "Spot Çekim <PERSON>", "spot-transfer-history": "Spot Transfer Geçmişi", "margin-assets": "Marj Varlıkları", "margin-borrowing-records": "<PERSON><PERSON>tlar<PERSON>", "financial-assets": "Finansal Varlıklar", "financial-records": "Finansal İşlemler Geçmişi", "amm-assets": "AMM Varlıkları", "amm-income-records": "AMM Gelir <PERSON>", "amm-records": "AMM İşlemler Geçmişi", "blog": "Blog"}, "footer": {"company": "Şirket", "contactUs": "Bize Ulaşın", "discord": "Discord", "support": "Destek", "faq": "SSS", "exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wallets": "Cüzdanlar", "fees": "<PERSON><PERSON><PERSON>", "api": "API", "academy": "Akademi", "terms": "Kullanım Koşulları", "status": "Durum", "helpCenter": "<PERSON><PERSON><PERSON>", "allRightsReserved": "<PERSON><PERSON>m hakları saklıdır.", "yourCryptoTradingExpert": "Kripto İşlemi Uzmanınız"}, "seo": {"home": {"title": "Safe Trade - Kripto Al, Kripto Sat, Kripto İşle", "description": "Safe Trade, her yatırı<PERSON><PERSON><PERSON> i<PERSON>, hızlı ve kullanıcı dostu bir platformdur."}, "markets-coin": {"title": "Markets Coin", "description": "Safe Trade'te dinamik kripto piyasaları keşfedin – en popüler kripto para birimleri hakkında gerç<PERSON> zamanlı fiyatlar, trendler ve analizler."}, "markets-data": {"title": "Markets Data", "description": "Safe Trade'te transparan ve sezgisel kripto piyasa verilerine eri<PERSON>in, anlık ve bilgilendirici yatırım kararları alın."}, "swap": {"title": "<PERSON><PERSON><PERSON>", "description": "Safe Trade ile dijital varlıkları kolayca ve güvenle takas edin – hı<PERSON><PERSON><PERSON><PERSON>, düşük ücretler ve optimize edilmiş kullanıcı deneyimi."}, "auth-login": {"title": "<PERSON><PERSON>", "description": "Safe Trade'e giriş <PERSON>, varlıklarınızı yönetin ve kripto işlemlerinizi güvenli bir şekilde gerçekleştirin."}, "auth-register": {"title": "Register", "description": "Safe Trade hesabınızı yalnızca birkaç adımda oluşturun ve güvenli, transparan ve yatırım getirisi olan kripto işlemlerinizin dünyasını keşfedin."}, "exchange": {"title": "Safe Trade Exchange", "description": "Safe Trade Exchange, kripto işlemlerini güvenli ve verimli bir şekilde gerçekleştirmek için bir platformdur. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, düşük ücretler ve optimize edilmiş kullanıcı deneyimi ile kullanıcılarınızın ihtiyaçlarına cevap verin."}, "wallet-overview": {"title": "Cüzdan Genel Bakışı"}, "wallet-spot-assets": {"title": "Spot Varlıklar"}, "wallet-spot-withdraw": {"title": "<PERSON>ekim"}, "wallet-spot-deposit": {"title": "Spot Yatırma"}, "wallet-spot-deposit-record": {"title": "Spot Yatırma <PERSON>ı"}, "wallet-spot-withdraw-record": {"title": "Spot Çekim <PERSON>"}, "wallet-spot-transfer-history": {"title": "Spot Transfer Geçmişi"}, "wallet-margin-assets": {"title": "Marj Varlıkları"}, "wallet-margin-record": {"title": "<PERSON><PERSON>"}, "wallet-financial-assets": {"title": "Finansal Varlıklar"}, "wallet-financial-record": {"title": "Finansal <PERSON>"}, "wallet-amm-assets": {"title": "AMM Varlıkları"}, "wallet-amm-income-records": {"title": "AMM Gelir <PERSON>"}, "wallet-amm-records": {"title": "AMM İşlemler Geçmişi"}, "orders-open": {"title": "Açık <PERSON>ler"}, "orders-history": {"title": "<PERSON><PERSON>"}, "orders-trades": {"title": "<PERSON><PERSON>"}, "account-overview": {"title": "Hesap <PERSON>l Bakışı"}, "account-security": {"title": "<PERSON><PERSON><PERSON>"}, "account-preferences": {"title": "<PERSON><PERSON><PERSON>"}, "account-api-management": {"title": "Hesap API Yönetimi"}, "account-verify": {"title": "<PERSON><PERSON><PERSON>"}, "account-address-book": {"title": "<PERSON><PERSON><PERSON>"}, "account-fees-trading": {"title": "Hesap İşlem Ücretleri"}, "account-fees-transaction": {"title": "Hesap İşlem Ücretleri"}, "account-interactive-messages": {"title": "<PERSON>t<PERSON><PERSON><PERSON><PERSON>"}, "account-setting": {"title": "<PERSON><PERSON><PERSON>"}, "account-snapshots": {"title": "Hesap Anlık Görüntüleri"}, "account-confirm-code": {"title": "Ko<PERSON> Doğrula"}, "blog": {"title": "SafeTrade Blog", "description": "Dünyanın önde gelen kripto para borsasının gerçek zamanlı güncellemelerini ve trendlerini takip edin"}, "announcements": {"title": "Safe<PERSON><PERSON>", "description": "Safe Trade'den en son haberler ve duyuru<PERSON>an haberdar olun"}}, "blog": {"title": "SafeTrade Blog", "description": "Dünyanın önde gelen kripto para borsasının gerçek zamanlı güncellemelerini ve trendlerini takip edin", "followUs": "Bizi Takip Ed<PERSON>", "readMore": "Devamını Oku", "loadMore": "<PERSON><PERSON>", "hotTopics": "🔥 <PERSON><PERSON><PERSON>", "categories": {"title": "<PERSON><PERSON><PERSON>", "featured": "<PERSON><PERSON>"}, "telegram": {"title": "<PERSON><PERSON> derin piyasa içgörüleri için Telegram'a katılın", "description": "Gerçek zamanlı piyasa analizi ve ticaret içgörüleri için topluluğumuza bağlanın", "joinNow": "Şimdi <PERSON>"}, "publishedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shareThisArticle": "<PERSON><PERSON> Ma<PERSON><PERSON>", "all": "Tümü"}, "announcements": {"newListing": "<PERSON><PERSON>", "more": "<PERSON><PERSON>"}, "breadcrumb": {"home": "<PERSON>", "markets-coin": "<PERSON><PERSON><PERSON><PERSON>", "markets-data": "<PERSON><PERSON><PERSON>", "exchange": "Borsa", "swap": "<PERSON><PERSON>", "wallet-overview": "Cüzdan", "auth-login": "<PERSON><PERSON><PERSON>", "auth-register": "<PERSON><PERSON><PERSON>", "account-confirm-code": "Ko<PERSON> Doğrula"}}